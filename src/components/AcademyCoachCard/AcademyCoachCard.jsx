import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Image from '@d11/react-native-fast-image';
import styles from './styles';
import { useNavigation } from '@react-navigation/native';
const redirect = require('../../assets/redirect.png');

const AcademyCoachCard = ({ coach, isLast }) => {
  const navigation = useNavigation();
  const handlePress = () => {
    if (coach && coach._id) {
      navigation.navigate('CoachProfile', { ID: coach._id });
    }
  };
  return (
    <TouchableOpacity style={[styles.card, isLast && { marginRight: 0 }]} onPress={handlePress}> 
      {coach.profileImg ? (
        <Image
          source={{ uri: coach.profileImg }}
          style={styles.image}
        />
      ) : (
        <View style={[styles.image, { justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }]}> 
          <Text style={{ color: '#888', fontSize: 16, textAlign: 'center' }}>No Image</Text>
        </View>
      )}
      <View style={styles.cardContent}>
        <Text style={styles.coachName}>
          {coach.firstName} {coach.lastName}
        </Text>
        <Text style={styles.category}>
          {coach.sportsCategories && coach.sportsCategories.length > 0 ? coach.sportsCategories[0] : 'N/A'}
        </Text>
        <Text style={styles.description} numberOfLines={2}>
          {coach.firstName} is well known in {coach.sportsCategories && coach.sportsCategories.length > 0 ? coach.sportsCategories[0] : 'N/A'} of India with {coach.experience || 0} years of experience.
        </Text>
        <View style={styles.learnMore}>
          <Text style={styles.learnMoreText}>What will you learn</Text>
          <Image source={redirect} style={{ width: 15, height: 15 }} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default AcademyCoachCard; 