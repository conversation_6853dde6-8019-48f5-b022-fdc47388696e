import { StyleSheet } from 'react-native';
import { CARD_DIMENSIONS } from '../../constants/cardDimensions';

export default StyleSheet.create({
  card: {
    width: CARD_DIMENSIONS.STANDARD_CARD_WIDTH,
    borderWidth: CARD_DIMENSIONS.CARD_BORDER_WIDTH,
    borderColor: CARD_DIMENSIONS.CARD_BORDER_COLOR,
    borderRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    backgroundColor: '#FFF',
    overflow: 'hidden', // Added for consistency with CourseCard
  },
  image: {
    width: '100%',
    height: CARD_DIMENSIONS.COACH_CARD_IMAGE_HEIGHT,
    borderTopLeftRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    borderTopRightRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    objectFit: 'fill',
  },
  cardContent: {
    padding: 8,
    justifyContent: 'space-between',
    height: 150,
  },
  coachName: {
    fontSize: 16,
    color: 'black',
    fontFamily: 'Lato-Bold',
  },
  category: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Lato-Regular',
  },
  description: {
    fontSize: 14,
    color: 'grey',
    marginTop: 4,
    fontFamily: 'Lato-Regular',
  },
  learnMore: {
    borderRadius: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: '0.5%',
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
}); 