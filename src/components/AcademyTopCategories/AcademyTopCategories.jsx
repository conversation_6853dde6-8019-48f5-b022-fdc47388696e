import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';

const AcademyTopCategories = ({ categories, title }) => {
  const navigation = useNavigation();

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{title || 'CATEGORIES'}</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        contentContainerStyle={styles.scrollViewContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category._id}
            style={styles.category}
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate('Collection', { selectedOption: category.lowerCaseName })
            }
          >
            <Image
              source={{ uri: category.image }}
              style={styles.image}
            />
            <Text style={styles.categoryName}>{category.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
  heading: {
    fontFamily: 'Lato-Semibold',
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 64,
    letterSpacing: 0.72,
    textTransform: 'uppercase',
    color: '#000',
  },
  scrollViewContent: {
    gap: 12, // Reduced from s(16)
  },
  category: {
    width: 120, // Reduced from s(160) - about 75% of original
    height: 165, // Reduced from s(219) - about 75% of original
    alignItems: 'center',
    marginRight: 12, // Reduced from s(16)
    borderRadius: 8, // Reduced from s(10)
    gap: 10, // Reduced from vs(14)
    opacity: 1,
  },
  image: {
    width: 120, // Reduced from s(160) - matches container width
    height: 120, // Reduced from s(160) - maintains square aspect
    borderRadius: 8, // Reduced from s(10)
    objectFit: 'contain',
  },
  categoryName: {
    fontFamily: 'Lato-Medium',
    fontWeight: '500',
    fontSize: 14, // Reduced from ms(18) - about 78% of original
    lineHeight: 20, // Reduced from vs(45) - more reasonable line height
    letterSpacing: 0,
    textAlign: 'center',
    textAlignVertical: 'center',
    color: '#000',
  },
});

export default AcademyTopCategories;
