import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { s, vs, ms } from 'react-native-size-matters';

const AcademyTopCategories = ({ categories, title }) => {
  const navigation = useNavigation();

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{title || 'CATEGORIES'}</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        contentContainerStyle={styles.scrollViewContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category._id}
            style={styles.category}
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate('Collection', { selectedOption: category.lowerCaseName })
            }
          >
            <Image
              source={{ uri: category.image }}
              style={styles.image}
            />
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
  heading: {
    fontFamily: 'Lato-Semibold',
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 64,
    letterSpacing: 0.72,
    textTransform: 'uppercase',
    color: '#000',
  },
  scrollViewContent: {
    gap: 16,
  },
  category: {
    width: screenWidth * 0.35,
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: screenHeight * 0.20,
    borderRadius: 25,
    objectFit: 'contain',
  },
});

export default AcademyTopCategories;
