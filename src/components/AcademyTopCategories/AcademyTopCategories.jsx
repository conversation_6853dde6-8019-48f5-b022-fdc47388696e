import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { s, vs, ms } from 'react-native-size-matters';

const AcademyTopCategories = ({ categories, title }) => {
  const navigation = useNavigation();

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{title || 'CATEGORIES'}</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        contentContainerStyle={styles.scrollViewContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category._id}
            style={styles.category}
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate('Collection', { selectedOption: category.lowerCaseName })
            }
          >
            <Image
              source={{ uri: category.image }}
              style={styles.image}
            />
            <Text style={styles.categoryName}>{category.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
  heading: {
    fontFamily: 'Lato-Semibold',
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 64,
    letterSpacing: 0.72,
    textTransform: 'uppercase',
    color: '#000',
  },
  scrollViewContent: {
    gap: s(16),
  },
  category: {
    width: s(160), // width: 160
    height: s(219), // height: 219
    alignItems: 'center',
    marginRight: s(16),
    borderRadius: s(10), // border-radius: 10px
    gap: vs(14), // gap: 14px
    opacity: 1, // opacity: 1
    // angle: 0 deg is default (no rotation needed)
  },
  image: {
    width: s(160), // Image width: 160
    height: s(160), // Image height: 160
    borderRadius: s(10), // Image border-radius: 10px
    objectFit: 'contain',
  },
  categoryName: {
    fontFamily: 'Lato-Medium', // font-family: Lato, font-style: Medium
    fontWeight: '500', // font-weight: 500
    fontSize: ms(18), // font-size: 18px
    lineHeight: vs(45), // line-height: 45px
    letterSpacing: 0, // letter-spacing: 0%
    textAlign: 'center', // text-align: center
    textAlignVertical: 'center', // vertical-align: middle
    color: '#000',
  },
});

export default AcademyTopCategories;
