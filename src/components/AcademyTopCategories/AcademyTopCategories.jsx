import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { moderateScale, verticalScale } from 'react-native-size-matters';

const AcademyTopCategories = ({ categories, title }) => {
  const navigation = useNavigation();

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{title || 'CATEGORIES'}</Text>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        contentContainerStyle={styles.scrollViewContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category._id}
            style={styles.category}
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate('Collection', { selectedOption: category.lowerCaseName })
            }
          >
            <Image
              source={{ uri: category.image }}
              style={styles.image}
            />
            <Text style={styles.categoryName}>{category.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
  heading: {
    fontFamily: 'Lato-Semibold',
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 64,
    letterSpacing: 0.72,
    textTransform: 'uppercase',
    color: '#000',
  },
  scrollViewContent: {
    gap: moderateScale(12), // Using smaller base value with moderate scaling
  },
  category: {
    width: moderateScale(120), // Using 120 as base instead of 160
    height: moderateScale(165), // Using 165 as base instead of 219
    alignItems: 'center',
    marginRight: moderateScale(12),
    borderRadius: moderateScale(8),
    gap: verticalScale(8), // Using smaller gap
    opacity: 1,
  },
  image: {
    width: moderateScale(120), // Matches container width
    height: moderateScale(120), // Square aspect ratio
    borderRadius: moderateScale(8),
    objectFit: 'contain',
  },
  categoryName: {
    fontFamily: 'Lato-Medium',
    fontWeight: '500',
    fontSize: moderateScale(14), // Using smaller base value with moderate scaling
    lineHeight: verticalScale(18), // More reasonable line height with vertical scaling
    letterSpacing: 0,
    textAlign: 'center',
    textAlignVertical: 'center',
    color: '#000',
  },
});

export default AcademyTopCategories;
