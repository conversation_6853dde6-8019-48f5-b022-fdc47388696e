import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Dimensions, ActivityIndicator, TouchableOpacity, RefreshControl } from 'react-native';
import { styles, htmlTagStyles } from './styles';
import Footer from '../../components/footer';
import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import HTML from 'react-native-render-html';
import AcademyImageGallery from '../../components/AcademyImageGallery';
import { vs } from 'react-native-size-matters';
import WhyPeopleLoveKhelSports from '../../components/homepage/WhyPeopleLoveKhelSports.jsx';
import AcademyTopCourses from '../../components/AcademyTopCourses/AcademyTopCourses.jsx';
import AcademyCoach from '../../components/AcademyCoach/AcademyCoach.jsx';
import AcademyTopCategories from '../../components/AcademyTopCategories/AcademyTopCategories.jsx';
import SearchLocationContainer from '../../components/SearchLocationContainer/SearchLocationContainer';
import FacilityCardContainer from '../../components/FacilityCardContainer/FacilityCardContainer.tsx';
import Image from '@d11/react-native-fast-image';

// Helper to capitalize first letter of each word
const capitalizeWords = (str: string) =>
  str.replace(/\b\w/g, (char) => char.toUpperCase());

// Helper to validate URLs
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Helper to process HTML content and handle invalid URLs
const processHtmlContent = (html: string): string => {
  // Replace invalid URLs with plain text
  return html.replace(
    /<a\s+href="([^"]*)"[^>]*>(.*?)<\/a>/g,
    (match, href, text) => {
      if (isValidUrl(href)) {
        // Keep valid URLs as links
        return match;
      } else {
        // Convert invalid URLs to plain text with strikethrough styling
        return `<span style="color: #999; text-decoration: line-through;">${text}</span>`;
      }
    }
  );
};

// Loading component
const LoadingIndicator = () => (
  <View style={styles.loaderContainer}>
    <ActivityIndicator size="large" color="#0000ff" />
    <Text style={styles.loadingText}>Loading academy details...</Text>
  </View>
);

interface ErrorViewProps {
  error: string;
  onRetry: () => void;
  onGoBack: () => void;
}

const ErrorView: React.FC<ErrorViewProps> = ({ error, onRetry, onGoBack }) => (
  <View style={styles.errorContainer}>
    <Text style={styles.errorTitle}>Oops!</Text>
    <Text style={styles.errorMessage}>{error}</Text>
    <View style={styles.buttonContainer}>
      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.goBackButton} onPress={onGoBack}>
        <Text style={styles.goBackButtonText}>Go Back</Text>
      </TouchableOpacity>
    </View>
  </View>
);





// Fixed collection names for academy blocks
const ACADEMY_COLLECTIONS = [
  'academyFacilities',
  'academyTopCoachCms',
  'academyTopCourseCms',
  'academyTestimonial',
  'academyDescription',
];

const AcademyProfile = ({ navigation, route }: any) => {
  const { id } = route.params;
  console.log("Academy Profile ID: -------------->\n\n\n\n\n\n", id);

  // Academy basic data states (from new API)
  const [academyData, setAcademyData] = useState<any>(null);
  const [title, setTitle] = useState<string>('');
  const [profileImg, setProfileImg] = useState<string | null>(null);
  const [academyImages, setAcademyImages] = useState<string[]>([]);

  // Data states
  const [blockData, setBlockData] = useState<any>({});
  const [description, setDescription] = useState<string | null>(null);
  const [topCoaches, setTopCoaches] = useState<any[]>([]);
  const [facilities, setFacilities] = useState<any[]>([]);
  const [testimonials, setTestimonials] = useState<Array<{
    _id: string;
    name: string;
    description: string;
    image?: string;
    position?: number;
  }>>([]);
  const [courses, setCourses] = useState<Array<{ _id: string; course: { status?: string; [key: string]: any } }>>([]);
  const [categories, setCategories] = useState<any[]>([]);

  // Loading and error states
  const [loadingStates, setLoadingStates] = useState({
    academyData: true,
    description: true,
    coaches: true,
    facilities: true,
    testimonials: true,
    courses: true,
    categories: true,
    blocks: true,
  });
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const { width } = Dimensions.get('window');

  const handleError = (error: any, section: string) => {
    console.error(`Error fetching ${section}:`, error);
    if (error.response?.status === 404) {
      setError('Academy not found. Please try again.');
    } else if (error.response?.status >= 500) {
      setError('Server error. Please try again later.');
    } else if (error.code === 'NETWORK_ERROR') {
      setError('Network error. Please check your connection and try again.');
    } else {
      setError('Something went wrong. Please try again.');
    }
  };

  // Fetch academy basic data from new API
  const fetchAcademyData = async () => {
    setLoadingStates(prev => ({ ...prev, academyData: true }));
    try {
      const academyUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy/${id}`;
      const academyResponse = await axios.get(academyUrl);
      const data = academyResponse.data.data;

      console.log("Academy Data Response: -------------->", data);

      setAcademyData(data);
      setTitle(data.name || '');
      setProfileImg(data.profileImage || null);
      setAcademyImages(data.academyImages || []);

      setLoadingStates(prev => ({ ...prev, academyData: false }));
    } catch (error) {
      handleError(error, 'academy data');
      setLoadingStates(prev => ({ ...prev, academyData: false }));
    }
  };

  // Fetch academy categories
  const fetchCategories = async () => {
    setLoadingStates(prev => ({ ...prev, categories: true }));
    try {
      const categoriesUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/categories`;
      const categoriesResponse = await axios.get(categoriesUrl);
      const data = categoriesResponse.data.data;

      console.log("Categories Response: -------------->", data);

      setCategories(data.categories || []);
      setLoadingStates(prev => ({ ...prev, categories: false }));
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategories([]);
      setLoadingStates(prev => ({ ...prev, categories: false }));
    }
  };

  // Fetch blocks and then fetch each collection if present
  const fetchBlocksAndCollections = async () => {
    setError(null);
    setLoadingStates(prev => ({ ...prev, blocks: true }));
    try {
      // Fetch blocks
      const blocksUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/blocks`;
      const blocksResponse = await axios.get(blocksUrl);
      const blocks = blocksResponse.data.data || [];
      console.log("Blocks Response: -------------->", blocks);
      // Map collectionName to block
      const blockMap: Record<string, any> = {};
      blocks.forEach((block: any) => {
        const collection = block.blockData.collectionName;
        if (collection) {
          blockMap[collection] = block;
        }
      });
      setBlockData(blockMap);
      setLoadingStates(prev => ({ ...prev, blocks: false }));
      // For each collection, if present in blockMap, fetch its data
      await Promise.all(
        ACADEMY_COLLECTIONS.map(async (collection) => {
          if (blockMap[collection]) {
            try {
              switch (collection) {
                case 'academyDescription': {
                  setLoadingStates(prev => ({ ...prev, description: true }));
                  const descUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/description`;
                  const descResponse = await axios.get(descUrl);
                  setDescription(descResponse.data.data?.description || null);
                  setLoadingStates(prev => ({ ...prev, description: false }));
                  break;
                }
                case 'academyTopCoachCms': {
                  setLoadingStates(prev => ({ ...prev, coaches: true }));
                  const coachUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/coach`;
                  const coachResponse = await axios.get(coachUrl);
                  const coaches = coachResponse.data.data
                    .filter((item: any) => item.coach?.status === 'active')
                    .map((item: any) => ({
                      ...item.coach,
                      position: item.position,
                    }))
                    .sort((a: any, b: any) => a.position - b.position);
                    
                  setTopCoaches(coaches);
                  setLoadingStates(prev => ({ ...prev, coaches: false }));
                  break;
                }
                case 'academyFacilities': {
                  setLoadingStates(prev => ({ ...prev, facilities: true }));
                  const facilitiesUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/facilities/active`;
                  const facilitiesResponse = await axios.get(facilitiesUrl);
                  setFacilities(facilitiesResponse.data.data || []);
                  setLoadingStates(prev => ({ ...prev, facilities: false }));
                  break;
                }
                case 'academyTestimonial': {
                  setLoadingStates(prev => ({ ...prev, testimonials: true }));
                  const testimonialsUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/testimonials`;
                  const testimonialsResponse = await axios.get(testimonialsUrl);
                  setTestimonials(testimonialsResponse.data.data || []);
                  setLoadingStates(prev => ({ ...prev, testimonials: false }));
                  break;
                }
                case 'academyTopCourseCms': {
                  setLoadingStates(prev => ({ ...prev, courses: true }));
                  const coursesUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/course`;
                  const coursesResponse = await axios.get(coursesUrl);
                  console.log("Courses Response: -------------->", coursesResponse.data.data);
                  setCourses(coursesResponse.data.data || []);
                  setLoadingStates(prev => ({ ...prev, courses: false }));
                  break;
                }
                default:
                  break;
              }
            } catch (error) {
              handleError(error, collection);
              setLoadingStates(prev => ({ ...prev, [collection]: false }));
            }
          }
        })
      );
    } catch (error) {
      handleError(error, 'blocks');
      setLoadingStates(prev => ({ ...prev, blocks: false }));
    }
    finally {
      setLoadingStates({
        academyData: false,
        description: false,
        coaches: false,
        facilities: false,
        testimonials: false,
        courses: false,
        categories: false,
        blocks: false,
      });
    }
  };

  // Combined fetch function
  const fetchAllData = async () => {
    await Promise.all([
      fetchAcademyData(),
      fetchCategories(),
      fetchBlocksAndCollections()
    ]);
  };

  useEffect(() => {
    fetchAllData();
  }, [id]);

  const handleRetry = () => {
    setError(null);
    setLoadingStates({
      academyData: true,
      description: true,
      coaches: true,
      facilities: true,
      testimonials: true,
      courses: true,
      categories: true,
      blocks: true,
    });
    fetchAllData();
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchAllData();
    setRefreshing(false);
  };

  // If there's an error, show error view
  if (error) {
    return (
      <ErrorView 
        error={error}
        onRetry={handleRetry}
        onGoBack={handleGoBack}
      />
    );
  }

  // If any section is loading, show loading indicator
  const loading = Object.values(loadingStates).some(loading => loading);
  if (loading) {
    return <LoadingIndicator />;
  }

  // Map testimonials to WhyPeopleLoveKhelSports format
  const testimonialData: Array<{ name: string; description: string; image?: string; position: number }> = testimonials.map(t => ({
    name: t.name,
    description: t.description,
    image: t.image, // or undefined if not present
    position: t.position || 0,
  }));



  // Sort backend-driven components by blockData.position
  const backendSections = Object.entries(blockData)
    .map(([collectionName, block]) => ({
      collectionName,
      position: (block as any)?.blockData?.position ?? 999,
      block,
    }))
    .sort((a, b) => a.position - b.position);

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          enabled={!loading && !error}
        />
      }
    >
      <SearchLocationContainer />
      <View style={styles.contentContainer}>
        {/* Fixed order sections - regardless of backend positions */}

        {/* 1. Profile Image */}
        {profileImg && (
          <Image
            source={{ uri: profileImg }}
            style={{ width: "100%", height: vs(200), borderRadius: 10, alignSelf: 'center', marginBottom: 16 }}
            resizeMode="cover"
          />
        )}

        {/* 2. Title */}
        {title && <Text style={styles.title}>{capitalizeWords(title)}</Text>}

        {/* 3. Description */}
        {description && (
          <HTML
            contentWidth={width}
            tagsStyles={htmlTagStyles}
            source={{ html: processHtmlContent(description) }}
          />
        )}

        {/* 4. Image Gallery */}
        {academyImages && Array.isArray(academyImages) && academyImages.length > 0 && (
          <AcademyImageGallery images={academyImages} />
        )}

        {/* 5. Top Categories */}
        {categories && categories.length > 0 && (
          <AcademyTopCategories categories={categories} title="TOP CATEGORIES" />
        )}

        {/* 6. Render other backend-driven sections (coaches, courses, facilities) */}
        {backendSections.map(({ collectionName, block }) => {
          if (!block || typeof block !== 'object' || block === null || !('blockData' in block) || typeof block.blockData !== 'object' || block.blockData === null) return null;
          const blockDataAny = block.blockData as any;
          switch (collectionName) {
            case 'academyDescription':
              // Description is now rendered in fixed position above, skip here
              return null;
            case 'academyTopCoachCms': {
              const max = blockDataAny.max;
              const limitedCoaches = max ? topCoaches.slice(0, max) : topCoaches;
              const sectionTitle = blockDataAny.title || 'COACHES';
              return (
                <View key={collectionName}>
                  <AcademyCoach coaches={limitedCoaches} title={sectionTitle} />
                </View>
              );
            }
            case 'academyTopCourseCms': {
              const max = blockDataAny.max;
              const activeCourses = courses.filter(c => c.course?.status === 'active');
              const limitedCourses = max ? activeCourses.slice(0, max) : activeCourses;
              const sectionTitle = blockDataAny.title || undefined;
              return (
                <View key={collectionName}>
                  <AcademyTopCourses courses={limitedCourses} title={sectionTitle} />
                </View>
              );
            }
            case 'academyFacilities': {
              const sectionTitle = blockDataAny.title || undefined;
              return (
                <View key={collectionName}>
                  <FacilityCardContainer facilities={facilities} title={sectionTitle} />
                </View>
              );
            }
            case 'academyTestimonial':
              // Testimonials are rendered separately below
              return null;
            default:
              return null;
          }
        })}

        {/* Testimonials section (if present) */}
        {blockData['academyTestimonial'] &&
          typeof blockData['academyTestimonial'] === 'object' &&
          blockData['academyTestimonial'] !== null &&
          'blockData' in blockData['academyTestimonial'] &&
          typeof (blockData['academyTestimonial'] as any).blockData === 'object' &&
          (blockData['academyTestimonial'] as any).blockData !== null &&
          testimonialData.length > 0 && (() => {
            const blockDataAny = (blockData['academyTestimonial'] as any).blockData;
            return (
              <View style={{ marginHorizontal: -20 }}>
                <WhyPeopleLoveKhelSports
                  data={testimonialData as any}
                  blockData={{ title: blockDataAny.title || `Here's why people ❤️ ${capitalizeWords(title)}` }}
                />
              </View>
            );
          })()}
      </View>
      <Footer />
    </ScrollView>
  );
};

export default AcademyProfile; 