import React, { useEffect, useState, ReactNode } from 'react';
import { View, Text, ScrollView, Dimensions, ActivityIndicator, TouchableOpacity, RefreshControl, Linking } from 'react-native';
import { styles, htmlTagStyles } from './styles';
import Footer from '../../components/footer';
import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import HTML from 'react-native-render-html';
import AcademyImageGallery from '../../components/AcademyImageGallery';
import { vs } from 'react-native-size-matters';
import WhyPeopleLoveKhelSports from '../../components/homepage/WhyPeopleLoveKhelSports.jsx';
import AcademyTopCourses from '../../components/AcademyTopCourses/AcademyTopCourses.jsx';
import AcademyCoach from '../../components/AcademyCoach/AcademyCoach.jsx';
import SearchLocationContainer from '../../components/SearchLocationContainer/SearchLocationContainer';
import FacilityCardContainer from '../../components/FacilityCardContainer/FacilityCardContainer.tsx';
import Image from '@d11/react-native-fast-image';

// Helper to capitalize first letter of each word
const capitalizeWords = (str: string) =>
  str.replace(/\b\w/g, (char) => char.toUpperCase());

// Helper to validate URLs
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Helper to process HTML content and handle invalid URLs
const processHtmlContent = (html: string): string => {
  // Replace invalid URLs with plain text
  return html.replace(
    /<a\s+href="([^"]*)"[^>]*>(.*?)<\/a>/g,
    (match, href, text) => {
      if (isValidUrl(href)) {
        // Keep valid URLs as links
        return match;
      } else {
        // Convert invalid URLs to plain text with strikethrough styling
        return `<span style="color: #999; text-decoration: line-through;">${text}</span>`;
      }
    }
  );
};

// Loading component
const LoadingIndicator = () => (
  <View style={styles.loaderContainer}>
    <ActivityIndicator size="large" color="#0000ff" />
    <Text style={styles.loadingText}>Loading academy details...</Text>
  </View>
);

interface ErrorViewProps {
  error: string;
  onRetry: () => void;
  onGoBack: () => void;
}

const ErrorView: React.FC<ErrorViewProps> = ({ error, onRetry, onGoBack }) => (
  <View style={styles.errorContainer}>
    <Text style={styles.errorTitle}>Oops!</Text>
    <Text style={styles.errorMessage}>{error}</Text>
    <View style={styles.buttonContainer}>
      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.goBackButton} onPress={onGoBack}>
        <Text style={styles.goBackButtonText}>Go Back</Text>
      </TouchableOpacity>
    </View>
  </View>
);

// Component configuration with positions
const componentConfig = [
  {
    id: 'profileImg',
    position: 1,
    component: 'ProfileImage',
    enabled: true
  },
  {
    id: 'title',
    position: 2,
    component: 'Title',
    enabled: true
  },
  {
    id: 'description',
    position: 3,
    component: 'Description',
    enabled: true
  },
  {
    id: 'imageGallery',
    position: 4,
    component: 'ImageGallery',
    enabled: true
  },
  {
    id: 'coaches',
    position: 5,
    component: 'Coaches',
    enabled: true
  },
  {
    id: 'trainingSchedule',
    position: 6,
    component: 'TrainingSchedule',
    enabled: true
  },
  {
    id: 'facilities',
    position: 7,
    component: 'Facilities',
    enabled: true
  }
];

// Sort components by position
const sortedComponents = componentConfig
  .filter(config => config.enabled)
  .sort((a, b) => a.position - b.position);





// Fixed collection names for academy blocks
const ACADEMY_COLLECTIONS = [
  'academyFacilities',
  'academyTopCoachCms',
  'academyTopCourseCms',
  'academyTestimonial',
  'academyDescription',
];

const AcademyProfile = ({ navigation, route }: any) => {
  const { id, academyImages, title, profileImg } = route.params;
  console.log("Academy Profile: -------------->\n\n\n\n\n\n", profileImg);
  // Data states
  const [blockData, setBlockData] = useState<any>({});
  const [description, setDescription] = useState<string | null>(null);
  const [topCoaches, setTopCoaches] = useState<any[]>([]);
  const [facilities, setFacilities] = useState<any[]>([]);
  const [testimonials, setTestimonials] = useState<Array<{
    _id: string;
    name: string;
    description: string;
    image?: string;
    position?: number;
  }>>([]);
  const [courses, setCourses] = useState<Array<{ _id: string; course: { status?: string; [key: string]: any } }>>([]);

  // Loading and error states
  const [loadingStates, setLoadingStates] = useState({
    description: true,
    coaches: true,
    facilities: true,
    testimonials: true,
    courses: true,
    blocks: true,
  });
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const { width } = Dimensions.get('window');

  const handleError = (error: any, section: string) => {
    console.error(`Error fetching ${section}:`, error);
    if (error.response?.status === 404) {
      setError('Academy not found. Please try again.');
    } else if (error.response?.status >= 500) {
      setError('Server error. Please try again later.');
    } else if (error.code === 'NETWORK_ERROR') {
      setError('Network error. Please check your connection and try again.');
    } else {
      setError('Something went wrong. Please try again.');
    }
  };

  // Fetch blocks and then fetch each collection if present
  const fetchBlocksAndCollections = async () => {
    setError(null);
    setLoadingStates(prev => ({ ...prev, blocks: true }));
    try {
      // Fetch blocks
      const blocksUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/blocks`;
      const blocksResponse = await axios.get(blocksUrl);
      const blocks = blocksResponse.data.data || [];
      console.log("Blocks Response: -------------->", blocks);
      // Map collectionName to block
      const blockMap: Record<string, any> = {};
      blocks.forEach((block: any) => {
        const collection = block.blockData.collectionName;
        if (collection) {
          blockMap[collection] = block;
        }
      });
      setBlockData(blockMap);
      setLoadingStates(prev => ({ ...prev, blocks: false }));
      // For each collection, if present in blockMap, fetch its data
      await Promise.all(
        ACADEMY_COLLECTIONS.map(async (collection) => {
          if (blockMap[collection]) {
            try {
              switch (collection) {
                case 'academyDescription': {
                  setLoadingStates(prev => ({ ...prev, description: true }));
                  const descUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/description`;
                  const descResponse = await axios.get(descUrl);
                  setDescription(descResponse.data.data?.description || null);
                  setLoadingStates(prev => ({ ...prev, description: false }));
                  break;
                }
                case 'academyTopCoachCms': {
                  setLoadingStates(prev => ({ ...prev, coaches: true }));
                  const coachUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/coach`;
                  const coachResponse = await axios.get(coachUrl);
                  const coaches = coachResponse.data.data
                    .filter((item: any) => item.coach?.status === 'active')
                    .map((item: any) => ({
                      ...item.coach,
                      position: item.position,
                    }))
                    .sort((a: any, b: any) => a.position - b.position);
                  setTopCoaches(coaches);
                  setLoadingStates(prev => ({ ...prev, coaches: false }));
                  break;
                }
                case 'academyFacilities': {
                  setLoadingStates(prev => ({ ...prev, facilities: true }));
                  const facilitiesUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/facilities/active`;
                  const facilitiesResponse = await axios.get(facilitiesUrl);
                  setFacilities(facilitiesResponse.data.data || []);
                  setLoadingStates(prev => ({ ...prev, facilities: false }));
                  break;
                }
                case 'academyTestimonial': {
                  setLoadingStates(prev => ({ ...prev, testimonials: true }));
                  const testimonialsUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/testimonials`;
                  const testimonialsResponse = await axios.get(testimonialsUrl);
                  setTestimonials(testimonialsResponse.data.data || []);
                  setLoadingStates(prev => ({ ...prev, testimonials: false }));
                  break;
                }
                case 'academyTopCourseCms': {
                  setLoadingStates(prev => ({ ...prev, courses: true }));
                  const coursesUrl = `${NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/course`;
                  const coursesResponse = await axios.get(coursesUrl);
                  console.log("Courses Response: -------------->", coursesResponse.data.data);
                  setCourses(coursesResponse.data.data || []);
                  setLoadingStates(prev => ({ ...prev, courses: false }));
                  break;
                }
                default:
                  break;
              }
            } catch (error) {
              handleError(error, collection);
              setLoadingStates(prev => ({ ...prev, [collection]: false }));
            }
          }
        })
      );
    } catch (error) {
      handleError(error, 'blocks');
      setLoadingStates(prev => ({ ...prev, blocks: false }));
    }
    finally {
      setLoadingStates({
        description: false,
        coaches: false,
        facilities: false,
        testimonials: false,
        courses: false,
        blocks: false,
      });
    }
  };

  useEffect(() => {
    fetchBlocksAndCollections();
  }, [id]);

  const handleRetry = () => {
    setError(null);
    setLoadingStates({
      description: true,
      coaches: true,
      facilities: true,
      testimonials: true,
      courses: true,
      blocks: true,
    });
    fetchBlocksAndCollections();
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchBlocksAndCollections();
    setRefreshing(false);
  };

  // If there's an error, show error view
  if (error) {
    return (
      <ErrorView 
        error={error}
        onRetry={handleRetry}
        onGoBack={handleGoBack}
      />
    );
  }

  // If any section is loading, show loading indicator
  const loading = Object.values(loadingStates).some(loading => loading);
  if (loading) {
    return <LoadingIndicator />;
  }

  // Map testimonials to WhyPeopleLoveKhelSports format
  const testimonialData: Array<{ name: string; description: string; image?: string; position: number }> = testimonials.map(t => ({
    name: t.name,
    description: t.description,
    image: t.image, // or undefined if not present
    position: t.position || 0,
  }));

  // Render component based on component type
  const renderComponent = (componentType: string) => {
    switch (componentType) {
      case 'ProfileImage':
        return profileImg ? (
          <Image
            source={profileImg}
            style={{ width: "100%", height: vs(200), borderRadius: 10, alignSelf: 'center', marginBottom: 16 }}
            resizeMode="cover"
          />
        ) : null;

      case 'Title':
        return title ? (
          <Text style={styles.title}>{capitalizeWords(title)}</Text>
        ) : null;

      case 'Description':
        return description ? (
          <HTML
            contentWidth={width}
            tagsStyles={htmlTagStyles}
            source={{ html: processHtmlContent(description) }}
          />
        ) : null;

      case 'ImageGallery':
        return academyImages?.length > 0 ? (
          <AcademyImageGallery images={academyImages} />
        ) : null;

      case 'Coaches': {
        if (!topCoaches?.length) return null;
        // Use dynamic title from blockData if available
        const block = blockData['academyTopCoachCms'];
        const sectionTitle = block?.blockData?.title || 'COACHES';
        return <AcademyCoach coaches={topCoaches} title={sectionTitle} />;
      }

      case 'TrainingSchedule': {
        const activeCourses = courses.filter(c => c.course?.status === 'active');
        if (!activeCourses.length) return null;
        // Use dynamic title from blockData if available
        const block = blockData['academyTopCourseCms'];
        const sectionTitle = block?.blockData?.title || undefined;
        return <AcademyTopCourses courses={activeCourses} title={sectionTitle} />;
      }

      case 'Facilities': {
        if (!facilities?.length) return null;
        // Use dynamic title from blockData if available
        const block = blockData['academyFacilities'];
        const sectionTitle = block?.blockData?.title || undefined;
        return <FacilityCardContainer facilities={facilities} title={sectionTitle} />;
      }

      default:
        return null;
    }
  };

  // Sort backend-driven components by blockData.position
  const backendSections = Object.entries(blockData)
    .map(([collectionName, block]) => ({
      collectionName,
      position: (block as any)?.blockData?.position ?? 999,
      block,
    }))
    .sort((a, b) => a.position - b.position);

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          enabled={!loading && !error}
        />
      }
    >
      <SearchLocationContainer />
      <View style={styles.contentContainer}>
        {/* 1. Profile Image */}
        {profileImg && (
          <Image
            source={profileImg}
            style={{ width: "100%", height: vs(200), borderRadius: 10, alignSelf: 'center', marginBottom: 16 }}
            resizeMode="cover"
          />
        )}
        {/* 2. Title */}
        {title && <Text style={styles.title}>{capitalizeWords(title)}</Text>}

        {/* 3. Render all backend-driven sections in position order */}
        {backendSections.map(({ collectionName, block }) => {
          if (!block || typeof block !== 'object' || block === null || !('blockData' in block) || typeof block.blockData !== 'object' || block.blockData === null) return null;
          const blockDataAny = block.blockData as any;
          switch (collectionName) {
            case 'academyDescription':
              return (
                <View key={collectionName}>
                  {renderComponent('Description')}
                  {/* Show Image Gallery right after description */}
                  {academyImages && Array.isArray(academyImages) && academyImages.length > 0 && (
                    <AcademyImageGallery images={academyImages} />
                  )}
                </View>
              );
            case 'academyTopCoachCms': {
              const max = blockDataAny.max;
              const limitedCoaches = max ? topCoaches.slice(0, max) : topCoaches;
              const sectionTitle = blockDataAny.title || 'COACHES';
              return (
                <View key={collectionName}>
                  <AcademyCoach coaches={limitedCoaches} title={sectionTitle} />
                </View>
              );
            }
            case 'academyTopCourseCms': {
              const max = blockDataAny.max;
              const activeCourses = courses.filter(c => c.course?.status === 'active');
              const limitedCourses = max ? activeCourses.slice(0, max) : activeCourses;
              const sectionTitle = blockDataAny.title || undefined;
              return (
                <View key={collectionName}>
                  <AcademyTopCourses courses={limitedCourses} title={sectionTitle} />
                </View>
              );
            }
            case 'academyFacilities': {
              const sectionTitle = blockDataAny.title || undefined;
              return (
                <View key={collectionName}>
                  <FacilityCardContainer facilities={facilities} title={sectionTitle} />
                </View>
              );
            }
            case 'academyTestimonial':
              // Testimonials are rendered separately below
              return null;
            case 'imageGallery':
              return <View key={collectionName}>{renderComponent('ImageGallery')}</View>;
            default:
              return null;
          }
        })}

        {/* Show Image Gallery after title if no description block exists */}
        {!backendSections.some(({ collectionName }) => collectionName === 'academyDescription') && 
         academyImages && Array.isArray(academyImages) && academyImages.length > 0 && (
          <AcademyImageGallery images={academyImages} />
        )}

        {/* Testimonials section (if present) */}
        {blockData['academyTestimonial'] &&
          typeof blockData['academyTestimonial'] === 'object' &&
          blockData['academyTestimonial'] !== null &&
          'blockData' in blockData['academyTestimonial'] &&
          typeof (blockData['academyTestimonial'] as any).blockData === 'object' &&
          (blockData['academyTestimonial'] as any).blockData !== null &&
          testimonialData.length > 0 && (() => {
            const blockDataAny = (blockData['academyTestimonial'] as any).blockData;
            return (
              <View style={{ marginHorizontal: -20 }}>
                <WhyPeopleLoveKhelSports
                  data={testimonialData as any}
                  blockData={{ title: blockDataAny.title || `Here's why people ❤️ ${capitalizeWords(title)}` }}
                />
              </View>
            );
          })()}
      </View>
      <Footer />
    </ScrollView>
  );
};

export default AcademyProfile; 